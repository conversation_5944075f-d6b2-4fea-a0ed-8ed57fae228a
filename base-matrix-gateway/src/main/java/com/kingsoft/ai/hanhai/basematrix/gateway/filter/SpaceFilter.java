package com.kingsoft.ai.hanhai.basematrix.gateway.filter;

import cn.dev33.satoken.reactor.context.SaReactorSyncHolder;
import cn.dev33.satoken.stp.StpUtil;
import com.kingsoft.ai.hanhai.basematrix.gateway.config.properties.CustomGatewayProperties;
import com.kingsoft.basematrix.common.space.utils.SpaceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 空间过滤器
 * <p>
 * 用于在请求头中添加空间ID
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpaceFilter implements GlobalFilter, Ordered {

    @Autowired
    private CustomGatewayProperties customGatewayProperties;

    private static final String SPACE_ID = "H-Space-Id";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//        if (!customGatewayProperties.getSpaceId()) {
//            return chain.filter(exchange);
//        }

        // 从请求头中获取 Authorization token
        String token = exchange.getRequest().getHeaders().getFirst("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            // 没有token，直接继续
            return chain.filter(exchange);
        }

        try {
            // 在响应式环境中，需要先设置上下文
            SaReactorSyncHolder.setContext(exchange);

            // 检查用户是否登录
            if (!StpUtil.isLogin()) {
                return chain.filter(exchange);
            }

            // 获取当前空间ID
            String currentSpaceId = SpaceUtils.getCurrentSpaceId();
            if (currentSpaceId != null) {
                log.info("空间ID: {}", currentSpaceId);
                // 创建新的请求，添加空间ID到请求头
                ServerHttpRequest newRequest = exchange.getRequest().mutate()
                    .header(SPACE_ID, currentSpaceId)
                    .build();
                // 创建新的exchange
                ServerWebExchange newExchange = exchange.mutate().request(newRequest).build();
                return chain.filter(newExchange);
            }
        } catch (Exception e) {
            // 如果获取登录状态失败，说明用户未登录，继续执行
            log.debug("获取用户登录状态失败，可能用户未登录: {}", e.getMessage());
        }

        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        // 确保在 Sa-Token 认证过滤器之后执行
        // Sa-Token 的 SaReactorFilter 默认顺序通常是 -100，我们设置为 -90 确保在其之后执行
        return -90;
    }
}
