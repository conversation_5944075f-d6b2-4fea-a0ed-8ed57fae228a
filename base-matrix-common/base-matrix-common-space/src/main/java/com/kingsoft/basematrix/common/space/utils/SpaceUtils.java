package com.kingsoft.basematrix.common.space.utils;

import com.kingsoft.ai.hanhai.basematrix.common.core.constant.CacheConstants;
import com.kingsoft.ai.hanhai.basematrix.common.core.constant.GlobalConstants;
import com.kingsoft.ai.hanhai.basematrix.common.core.utils.ObjectUtils;
import com.kingsoft.ai.hanhai.basematrix.common.redis.utils.RedisUtils;
import com.kingsoft.ai.hanhai.basematrix.common.satoken.utils.LoginHelper;

import java.time.Duration;

public class SpaceUtils {

    private static String getSpaceRedisKey() {
        String rnStr = LoginHelper.getTokenRnStr();
        String username = LoginHelper.getUsername();
        String key = GlobalConstants.GLOBAL_REDIS_KEY + CacheConstants.CURRENT_SPACE_ID + username + CacheConstants.SEPARATOR + rnStr;
        return key;
    }

    private static Long getExpirationTime() {
        Long maxTimeout = 1000 * 60 * 60 * 24 * 30L;
        Long tokenExpireTime = LoginHelper.getTokenTokenTimeout();
        if (tokenExpireTime == null || tokenExpireTime <= 0L) {
            return maxTimeout;
        }
        // 转换毫秒
        tokenExpireTime = tokenExpireTime * 1000;
        if (tokenExpireTime <= maxTimeout) {
            return tokenExpireTime;
        }
        // 默认30天
        return maxTimeout;
    }

    /**
     * 获取当前空间ID
     *
     * @return
     */
    public static String getCurrentSpaceId() {
        String key = getSpaceRedisKey();
        Object value = RedisUtils.getCacheObject(key);
        if (ObjectUtils.isNotNull(value)) {
            return value.toString();
        }
        return null;
    }

    /**
     * 设置当前空间ID
     *
     * @param spaceId
     */
    public static void setCurrentSpaceId(String spaceId) {
        String key = getSpaceRedisKey();
        //获取当前token过期时间
        Long expirationTime = getExpirationTime();
        Duration duration = Duration.ofMillis(expirationTime);
        RedisUtils.setCacheObject(key, spaceId, duration);
    }

    /**
     * 清除当前空间ID
     */
    public static Boolean clearCurrentSpaceId() {
        String key = getSpaceRedisKey();
        return RedisUtils.deleteObject(key);
    }
}
