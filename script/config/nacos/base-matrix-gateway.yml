# 安全配置
security:
  # 不校验白名单
  ignore:
    whites:
      - /auth/code
      - /auth/logout
      - /auth/login
      - /auth/binding/*
      - /auth/register
      - /auth/tenant/list
      - /resource/sms/code
      - /resource/sse/close
      - /*/v3/api-docs
      - /*/error
      - /csrf
      - /warm-flow-ui/**

spring:
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 基础平台配置  =============== start ================
        # 认证中心
        - id: base-matrix-auth
          uri: lb://base-matrix-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 代码生成
        - id: base-matrix-gen
          uri: lb://base-matrix-gen
          predicates:
            - Path=/tool/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: base-matrix-system
          uri: lb://base-matrix-system
          predicates:
            - Path=/system/**,/monitor/**
          filters:
            - StripPrefix=1
        # 资源服务
        - id: base-matrix-resource
          uri: lb://base-matrix-resource
          predicates:
            - Path=/resource/**
          filters:
            - StripPrefix=1
        # 基础平台配置  =============== end ================
        # 瀚海平台配置  =============== start ==============
        # hanhai  routh
        - id: ai-datasets-inner-routh
          uri: http://hanhai-data-port-inner.hanhai-data:8082
          predicates:
            - Path=/inner/datasets/api/**
          filters:
            - StripPrefix=2
        #对外tob-dev：
        - id: ai-datasets-standard-routh
          uri: http://hanhai-data-port-standard.hanhai-data:8083
          predicates:
            - Path=/standard/datasets/api/**
          filters:
            - StripPrefix=2
        #微调
        - id: ai-finetuning-routh
          uri: http://hanhai-data-port.hanhai-service:8083
          predicates:
            - Path=/finetuning/api/**
          filters:
            - StripPrefix=1

        - id: ai-training-routh
          uri: http://kas-backend-svc.kas:8008
          predicates:
            - Path=/training/api/**
          filters:
            - StripPrefix=1

        - id: ai-inference-routh
          uri: http://inference-service.hanhai-inference:8080
          predicates:
            - Path=/inference/api/**
          filters:
            - StripPrefix=1

        - id: ai-model-manage-routh
          uri: http://model-reg.hanhai-model:8088
          predicates:
            - Path=/model-manage/api/**
          filters:
            - StripPrefix=1
            - RewritePath=/api/?(?<segment>.*),/api-server/api/$\{segment}

        - id: ai-evaluation-routh
          uri: http://evaluation-svc.hanhai-evaluation:9160
          predicates:
            - Path=/evaluation/api/**

        - id: ai-evaluation-human-routh
          uri: http://hanhai-human-eval-svc.hanhai-human-eval:9162
          predicates:
            - Path=/evaluation-human/api/**
          filters:
            - StripPrefix=1
        # 瀚海平台配置  =============== end ================
        # 应用平台配置  =============== start ================
        - id: app-console-api-routh
          uri: http://app-ap-api.hanhai-app:5001
          predicates:
            - Path=/app/console/api
          filters:
            - StripPrefix=1

        - id: app-web-api-routh
          uri: http://app-ap-api.hanhai-app:5001
          predicates:
            - Path=/app/api
          filters:
            - StripPrefix=1

        - id: app-service-api-routh
          uri: http://app-ap-api.hanhai-app:5001
          predicates:
            - Path=/app/v1
          filters:
            - StripPrefix=1

        - id: app-files-api-routh
          uri: http://app-ap-api.hanhai-app:5001
          predicates:
            - Path=/app/files
          filters:
            - StripPrefix=1

        - id: app-plugin-daemon-routh
          uri: http://app-ap-plugin-daemon.hanhai-app:5002
          predicates:
            - Path=/app/e/
          filters:
            - StripPrefix=1

        - id: app-file-store-routh
          uri: http://app-ap-file-store.hanhai-app:8000
          predicates:
            - Path=/app/t/
          filters:
            - StripPrefix=1
        # 应用平台配置  =============== end ================
    # sentinel 配置
    sentinel:
      filter:
        enabled: false
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.server-addr}
            dataId: sentinel-${spring.application.name}.json
            groupId: ${spring.cloud.nacos.config.group}
            username: ${spring.cloud.nacos.username}
            password: ${spring.cloud.nacos.password}
            namespace: ${spring.profiles.active}
            data-type: json
            rule-type: gw-flow
